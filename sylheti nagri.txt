﻿Sylheti Language Learning App Plan
Features
1. Progressive Lessons:

   * Introduce Sylheti Nagri alphabets one at a time, starting with a single character (e.g., “ꠀ”) and progressing to multiple characters (e.g., two in Lesson 2).
   * Advance to syllables or words (e.g., “ꠀꠞ”) after completing all alphabets.
   * Ensure lessons build on previous content for a structured learning path.
   2. Text-Based Display:

      * Display Sylheti Nagri characters as text using Unicode (U+A800–U+A82F) with a custom font (e.g., Noto Sans Syloti Nagri or Surma Unicode).
      * Support dynamic rendering of individual characters and combinations to enable word formation.
      * Ensure consistent text rendering across iOS and Android devices.
      3. Audio Integration:

         * Play pre-recorded audio files for each character’s pronunciation automatically when the “Next” button is pressed.
         * Provide a “Repeat” button to allow users to replay audio as many times as desired.
         * Support audio for syllables/words (e.g., “ꠀꠞ”) following the same pattern.
         4. Interactive Tests:

            * Present multiple-choice questions (MCQs) with three characters (current and previous lesson items) displayed in random order.
            * Play a random sound from the displayed characters; users select the matching character.
            * Provide immediate feedback: green tick (✔) for correct answers, red cross (✘) for incorrect ones.
            * Add optional green border for correct selections and red border for incorrect ones.
            * Repeat each test set four times, removing correctly matched sounds from the pool after selection.
            * Allow users to skip tests and proceed to the next lesson item.
            5. Navigation Options:

               * Start Lesson: Begin a new lesson from the first uncompleted item.
               * Resume Last Session: Continue from the user’s last saved progress (lesson and item).
               * Repeat Last Session: Replay the previously completed lesson for review.
               * Go to Another Lesson: Open a menu to select any available lesson.
               6. Instruction Page:

                  * Display an initial screen with clear instructions on app usage (e.g., “Press Next to hear the sound, Repeat to replay, select the correct character in tests”).
                  * Include visual examples of buttons and feedback indicators (✔, ✘).
                  7. Progress Tracking:

                     * Save user progress (current lesson, current item, completed tests) in Firebase Firestore.
                     * Cache progress locally using AsyncStorage for offline access.
                     * Sync progress with Firestore when the device is online.
                     * Track test performance (correct/incorrect answers) to provide user feedback or analytics.
                     8. Post-Alphabet Content:

                        * Introduce syllables or words (e.g., “ꠀꠞ”) after completing all alphabets, using the same text-sound-test structure.
                        * Support expandable content for future lessons (e.g., phrases, sentences).
                        9. Randomized Test Content:

                           * Randomize character order in tests to enhance learning retention.
                           * Randomize sound playback order within each test set, ensuring variety.
                           * Remove a sound from the test pool once correctly matched to avoid repetition within a set.
                           10. Feedback Mechanism:

                              * Display visual feedback (✔ for correct, ✘ for incorrect) immediately after test selections.
                              * Implement optional colored borders (green for correct, red for incorrect) around selected characters.
                              * Ensure feedback is clear and accessible for all users (e.g., high-contrast visuals).
                              11. User Interface:

                                 * Design a clean, intuitive layout with centered text for characters and prominent buttons (“Next”, “Repeat”).
                                 * Ensure buttons are easily tappable with clear labels.
                                 * Use a consistent color scheme suitable for both light and dark themes.
                                 12. Offline Support:

                                    * Cache audio files and lesson data locally for offline use.
                                    * Allow lesson and test progression without an internet connection, syncing progress when online.
                                    13. Accessibility:

                                       * Ensure Sylheti Nagri text is legible with appropriate font size and contrast.
                                       * Support screen readers for accessibility (e.g., label buttons and text correctly).
                                       * Test audio clarity for users with hearing aids or varying audio quality.
                                       14. Scalability:

                                          * Structure data to support additional lessons (e.g., advanced vocabulary, grammar) in future updates.
                                          * Allow easy addition of new characters, sounds, or syllables via Firebase.
                                          15. Error Handling:

                                             * Handle cases where audio files fail to load (e.g., network issues) with user-friendly error messages.
                                             * Ensure font rendering fallbacks if the custom font fails on certain devices.
                                             * Provide retry options for failed test submissions or navigation actions.